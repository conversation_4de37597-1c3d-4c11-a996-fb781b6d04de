<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Professional Corporate Website{% endblock %}</title>
    
    <!-- TailwindCSS 3.4.17 -->
    <script src="https://cdn.tailwindcss.com?v=3.4.17"></script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js"></script>
    
    <!-- Custom TailwindCSS Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554'
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                            950: '#020617'
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .corporate-shadow {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .hover-lift {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .hover-lift:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
        }

        /* Alpine.js cloak */
        [x-cloak] {
            display: none !important;
        }

        /* Mobile menu animations */
        .animate-fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="h-full bg-gray-50 font-sans antialiased">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>
    
    <!-- Header -->
    {% include 'components/header.html' %}
    
    <!-- Main Content -->
    <main id="main-content" class="flex-1">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    {% include 'components/footer.html' %}
    
    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator fixed top-4 right-4 z-50">
        <div class="bg-primary-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
            <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm font-medium">Loading...</span>
        </div>
    </div>
    
    <!-- HTMX Configuration -->
    <script>
        document.body.addEventListener('htmx:configRequest', function(evt) {
            evt.detail.headers['X-CSRFToken'] = document.querySelector('[name=csrfmiddlewaretoken]')?.value;
        });

        // Add loading states
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            evt.target.classList.add('opacity-75', 'pointer-events-none');
        });

        document.body.addEventListener('htmx:afterRequest', function(evt) {
            evt.target.classList.remove('opacity-75', 'pointer-events-none');
        });

        // Fallback mobile menu functionality if Alpine.js fails
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking Alpine.js...');

            // Wait a bit for Alpine.js to initialize
            setTimeout(function() {
                if (typeof Alpine === 'undefined') {
                    console.log('Alpine.js not found, using fallback mobile menu');
                    initFallbackMobileMenu();
                } else {
                    console.log('Alpine.js found and working');
                }
            }, 1000);
        });

        function initFallbackMobileMenu() {
            const mobileMenuButton = document.querySelector('[aria-label="Toggle mobile menu"]');
            const mobileMenu = document.querySelector('.lg\\:hidden.bg-white.border-t.border-gray-200.shadow-2xl');
            const hamburgerIcon = document.querySelector('[aria-label="Toggle mobile menu"] svg:first-child');
            const closeIcon = document.querySelector('[aria-label="Toggle mobile menu"] svg:last-child');

            if (!mobileMenuButton || !mobileMenu) return;

            let isOpen = false;

            // Initially hide the menu and show hamburger icon
            mobileMenu.style.display = 'none';
            if (hamburgerIcon) hamburgerIcon.style.display = 'block';
            if (closeIcon) closeIcon.style.display = 'none';

            mobileMenuButton.addEventListener('click', function() {
                isOpen = !isOpen;

                if (isOpen) {
                    mobileMenu.style.display = 'block';
                    mobileMenu.classList.add('animate-fade-in');
                    if (hamburgerIcon) hamburgerIcon.style.display = 'none';
                    if (closeIcon) closeIcon.style.display = 'block';
                } else {
                    mobileMenu.style.display = 'none';
                    mobileMenu.classList.remove('animate-fade-in');
                    if (hamburgerIcon) hamburgerIcon.style.display = 'block';
                    if (closeIcon) closeIcon.style.display = 'none';
                }

                mobileMenuButton.setAttribute('aria-expanded', isOpen);
            });

            // Close menu when clicking on links
            const mobileLinks = mobileMenu.querySelectorAll('a');
            mobileLinks.forEach(link => {
                link.addEventListener('click', function() {
                    isOpen = false;
                    mobileMenu.style.display = 'none';
                    if (hamburgerIcon) hamburgerIcon.style.display = 'block';
                    if (closeIcon) closeIcon.style.display = 'none';
                    mobileMenuButton.setAttribute('aria-expanded', false);
                });
            });
        }
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
