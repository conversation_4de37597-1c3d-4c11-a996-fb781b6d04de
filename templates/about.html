{% extends "base.html" %}

{% block title %}About Us - CorporatePro | Professional Business Solutions{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-20 lg:py-32">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center" x-data="{ visible: false }" x-init="setTimeout(() => visible = true, 100)">
            <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6"
                x-show="visible" 
                x-transition:enter="transition ease-out duration-1000"
                x-transition:enter-start="opacity-0 transform translate-y-8"
                x-transition:enter-end="opacity-100 transform translate-y-0">
                About <span class="text-primary-400">CorporatePro</span>
            </h1>
            <p class="text-xl lg:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed"
               x-show="visible" 
               x-transition:enter="transition ease-out duration-1000 delay-200"
               x-transition:enter-start="opacity-0 transform translate-y-8"
               x-transition:enter-end="opacity-100 transform translate-y-0">
                Empowering businesses with innovative solutions, strategic insights, and unwavering commitment to excellence since 2009.
            </p>
        </div>
    </div>
</section>

<!-- Mission & Vision Section -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <!-- Content -->
            <div x-data="{ inView: false }" x-init="setTimeout(() => inView = true, 200)">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-8"
                    x-show="inView" 
                    x-transition:enter="transition ease-out duration-800"
                    x-transition:enter-start="opacity-0 transform translate-x-8"
                    x-transition:enter-end="opacity-100 transform translate-x-0">
                    Our Mission & Vision
                </h2>
                
                <div class="space-y-8">
                    <div x-show="inView" 
                         x-transition:enter="transition ease-out duration-800 delay-200"
                         x-transition:enter-start="opacity-0 transform translate-x-8"
                         x-transition:enter-end="opacity-100 transform translate-x-0">
                        <h3 class="text-xl font-semibold text-primary-600 mb-3 flex items-center">
                            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Mission
                        </h3>
                        <p class="text-gray-600 leading-relaxed">
                            To deliver exceptional business solutions that drive growth, efficiency, and innovation for our clients. We are committed to understanding unique challenges and providing tailored strategies that create lasting value.
                        </p>
                    </div>
                    
                    <div x-show="inView" 
                         x-transition:enter="transition ease-out duration-800 delay-400"
                         x-transition:enter-start="opacity-0 transform translate-x-8"
                         x-transition:enter-end="opacity-100 transform translate-x-0">
                        <h3 class="text-xl font-semibold text-primary-600 mb-3 flex items-center">
                            <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Vision
                        </h3>
                        <p class="text-gray-600 leading-relaxed">
                            To be the leading partner for businesses seeking transformation and growth. We envision a future where every organization can achieve its full potential through strategic innovation and expert guidance.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Visual Element -->
            <div class="relative" x-data="{ inView: false }" x-init="setTimeout(() => inView = true, 400)">
                <div x-show="inView" 
                     x-transition:enter="transition ease-out duration-1000"
                     x-transition:enter-start="opacity-0 transform scale-95"
                     x-transition:enter-end="opacity-100 transform scale-100">
                    <div class="relative">
                        <!-- Main shape -->
                        <div class="w-full max-w-md mx-auto">
                            <div class="aspect-square bg-gradient-to-br from-primary-500 to-primary-700 rounded-3xl shadow-2xl relative overflow-hidden">
                                <!-- Pattern overlay -->
                                <div class="absolute inset-0 opacity-20">
                                    <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                                        <defs>
                                            <pattern id="about-pattern" width="20" height="20" patternUnits="userSpaceOnUse">
                                                <circle cx="10" cy="10" r="2" fill="currentColor"/>
                                            </pattern>
                                        </defs>
                                        <rect width="100" height="100" fill="url(#about-pattern)" />
                                    </svg>
                                </div>
                                <!-- Content -->
                                <div class="absolute inset-0 flex items-center justify-center text-white">
                                    <div class="text-center">
                                        <svg class="w-20 h-20 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                        <p class="font-semibold text-lg">Excellence</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Accent elements -->
                        <div class="absolute -top-4 -right-4 w-24 h-24 bg-primary-300 rounded-2xl opacity-60 transform rotate-12"></div>
                        <div class="absolute -bottom-6 -left-6 w-32 h-32 bg-primary-200 rounded-3xl opacity-40 transform -rotate-6"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Values Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Our Core Values
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                The principles that guide everything we do and define who we are as an organization.
            </p>
        </div>
        
        <!-- Values Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" x-data="{ inView: false }" x-init="setTimeout(() => inView = true, 300)">
            <!-- Value 1 -->
            <div class="bg-white p-8 rounded-2xl shadow-lg hover-lift"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600"
                 x-transition:enter-start="opacity-0 transform translate-y-8"
                 x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Integrity</h3>
                <p class="text-gray-600 leading-relaxed">
                    We conduct business with the highest ethical standards, building trust through transparency and honest communication.
                </p>
            </div>
            
            <!-- Value 2 -->
            <div class="bg-white p-8 rounded-2xl shadow-lg hover-lift"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600 delay-100"
                 x-transition:enter-start="opacity-0 transform translate-y-8"
                 x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Innovation</h3>
                <p class="text-gray-600 leading-relaxed">
                    We embrace cutting-edge technologies and creative thinking to deliver solutions that drive competitive advantage.
                </p>
            </div>
            
            <!-- Value 3 -->
            <div class="bg-white p-8 rounded-2xl shadow-lg hover-lift"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600 delay-200"
                 x-transition:enter-start="opacity-0 transform translate-y-8"
                 x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Client Focus</h3>
                <p class="text-gray-600 leading-relaxed">
                    Our clients' success is our success. We prioritize understanding their needs and exceeding their expectations.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Team Section -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Meet Our Leadership Team
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Experienced professionals dedicated to driving your business forward with expertise and passion.
            </p>
        </div>
        
        <!-- Team Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" x-data="{ inView: false }" x-init="setTimeout(() => inView = true, 500)">
            <!-- Team Member 1 -->
            <div class="text-center group hover-lift"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600"
                 x-transition:enter-start="opacity-0 transform translate-y-8"
                 x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="relative mb-6">
                    <div class="w-32 h-32 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full mx-auto flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
                        <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Sarah Johnson</h3>
                <p class="text-primary-600 font-medium mb-3">Chief Executive Officer</p>
                <p class="text-gray-600 text-sm leading-relaxed">
                    15+ years of experience in strategic business development and organizational transformation.
                </p>
            </div>
            
            <!-- Team Member 2 -->
            <div class="text-center group hover-lift"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600 delay-100"
                 x-transition:enter-start="opacity-0 transform translate-y-8"
                 x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="relative mb-6">
                    <div class="w-32 h-32 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full mx-auto flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
                        <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Michael Chen</h3>
                <p class="text-primary-600 font-medium mb-3">Chief Technology Officer</p>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Technology visionary with expertise in digital transformation and innovative solution architecture.
                </p>
            </div>
            
            <!-- Team Member 3 -->
            <div class="text-center group hover-lift"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600 delay-200"
                 x-transition:enter-start="opacity-0 transform translate-y-8"
                 x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="relative mb-6">
                    <div class="w-32 h-32 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full mx-auto flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
                        <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Emily Rodriguez</h3>
                <p class="text-primary-600 font-medium mb-3">Head of Client Success</p>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Dedicated to ensuring exceptional client experiences and building long-term partnerships.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-primary-600 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl lg:text-4xl font-bold mb-6">
            Ready to Work Together?
        </h2>
        <p class="text-xl text-primary-100 mb-8 leading-relaxed">
            Let's discuss how CorporatePro can help transform your business and achieve your goals.
        </p>
        <a href="/contact" 
           class="inline-block bg-white text-primary-700 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary-50 transition-all duration-200 hover-lift corporate-shadow">
            Get in Touch
        </a>
    </div>
</section>
{% endblock %}
