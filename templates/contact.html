{% extends "base.html" %}

{% block title %}Contact Us - CorporatePro | Get in Touch{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white py-20 lg:py-32">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center" x-data="{ visible: false }" x-init="setTimeout(() => visible = true, 100)">
            <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6"
                x-show="visible" 
                x-transition:enter="transition ease-out duration-1000"
                x-transition:enter-start="opacity-0 transform translate-y-8"
                x-transition:enter-end="opacity-100 transform translate-y-0">
                Get in <span class="text-primary-200">Touch</span>
            </h1>
            <p class="text-xl lg:text-2xl text-primary-100 max-w-4xl mx-auto leading-relaxed"
               x-show="visible" 
               x-transition:enter="transition ease-out duration-1000 delay-200"
               x-transition:enter-start="opacity-0 transform translate-y-8"
               x-transition:enter-end="opacity-100 transform translate-y-0">
                Ready to transform your business? Let's start the conversation and explore how we can help you achieve your goals.
            </p>
        </div>
    </div>
</section>

<!-- Contact Form & Info Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <!-- Contact Form -->
            <div class="bg-white rounded-2xl shadow-xl p-8 lg:p-12" x-data="{ visible: false }" x-init="setTimeout(() => visible = true, 100)">
                <h2 class="text-3xl font-bold text-gray-900 mb-8"
                    x-show="visible"
                    x-transition:enter="transition ease-out duration-800"
                    x-transition:enter-start="opacity-0 transform translate-y-8"
                    x-transition:enter-end="opacity-100 transform translate-y-0">
                    Send us a Message
                </h2>

                <!-- Form Success/Error Messages -->
                <div id="form-messages" class="mb-6"></div>

                <form hx-post="/contact"
                      hx-target="#form-messages"
                      hx-swap="innerHTML"
                      hx-indicator="#form-loading"
                      class="space-y-6"
                      x-show="visible"
                      x-transition:enter="transition ease-out duration-800 delay-200"
                      x-transition:enter-start="opacity-0 transform translate-y-8"
                      x-transition:enter-end="opacity-100 transform translate-y-0">
                    
                    <!-- Name Fields -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm font-semibold text-gray-700 mb-2">
                                First Name *
                            </label>
                            <input type="text" 
                                   id="first_name" 
                                   name="first_name" 
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                                   placeholder="John">
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-semibold text-gray-700 mb-2">
                                Last Name *
                            </label>
                            <input type="text" 
                                   id="last_name" 
                                   name="last_name" 
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                                   placeholder="Doe">
                        </div>
                    </div>
                    
                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">
                            Email Address *
                        </label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                               placeholder="<EMAIL>">
                    </div>
                    
                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">
                            Phone Number
                        </label>
                        <input type="tel" 
                               id="phone" 
                               name="phone"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                               placeholder="+****************">
                    </div>
                    
                    <!-- Company -->
                    <div>
                        <label for="company" class="block text-sm font-semibold text-gray-700 mb-2">
                            Company
                        </label>
                        <input type="text" 
                               id="company" 
                               name="company"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                               placeholder="Your Company Name">
                    </div>
                    
                    <!-- Subject -->
                    <div>
                        <label for="subject" class="block text-sm font-semibold text-gray-700 mb-2">
                            Subject *
                        </label>
                        <select id="subject" 
                                name="subject" 
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200">
                            <option value="">Select a subject</option>
                            <option value="general">General Inquiry</option>
                            <option value="consultation">Business Consultation</option>
                            <option value="partnership">Partnership Opportunity</option>
                            <option value="support">Support Request</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <!-- Message -->
                    <div>
                        <label for="message" class="block text-sm font-semibold text-gray-700 mb-2">
                            Message *
                        </label>
                        <textarea id="message" 
                                  name="message" 
                                  rows="6" 
                                  required
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 resize-vertical"
                                  placeholder="Tell us about your project or how we can help you..."></textarea>
                    </div>
                    
                    <!-- Privacy Consent -->
                    <div class="flex items-start space-x-3">
                        <input type="checkbox" 
                               id="privacy_consent" 
                               name="privacy_consent" 
                               required
                               class="mt-1 w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="privacy_consent" class="text-sm text-gray-600">
                            I agree to the <a href="#" class="text-primary-600 hover:text-primary-700 underline">Privacy Policy</a> and consent to being contacted regarding my inquiry. *
                        </label>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="pt-4">
                        <button type="submit" 
                                class="w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-200 hover-lift corporate-shadow flex items-center justify-center space-x-2">
                            <span>Send Message</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </button>
                        
                        <!-- Loading Indicator -->
                        <div id="form-loading" class="htmx-indicator mt-4 text-center">
                            <div class="inline-flex items-center space-x-2 text-primary-600">
                                <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span class="text-sm font-medium">Sending message...</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Contact Information -->
            <div class="space-y-8" x-data="{ visible: false }" x-init="setTimeout(() => visible = true, 200)">
                <!-- Contact Details -->
                <div class="bg-white rounded-2xl shadow-xl p-8"
                     x-show="visible"
                     x-transition:enter="transition ease-out duration-800"
                     x-transition:enter-start="opacity-0 transform translate-y-8"
                     x-transition:enter-end="opacity-100 transform translate-y-0">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Contact Information</h3>
                    
                    <div class="space-y-6">
                        <!-- Address -->
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-1">Office Address</h4>
                                <p class="text-gray-600">123 Business Avenue<br>Suite 100, City, State 12345</p>
                            </div>
                        </div>
                        
                        <!-- Phone -->
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-1">Phone</h4>
                                <p class="text-gray-600">
                                    <a href="tel:+1234567890" class="hover:text-primary-600 transition-colors duration-200">
                                        +****************
                                    </a>
                                </p>
                            </div>
                        </div>
                        
                        <!-- Email -->
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-1">Email</h4>
                                <p class="text-gray-600">
                                    <a href="mailto:<EMAIL>" class="hover:text-primary-600 transition-colors duration-200">
                                        <EMAIL>
                                    </a>
                                </p>
                            </div>
                        </div>
                        
                        <!-- Hours -->
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-1">Business Hours</h4>
                                <p class="text-gray-600">
                                    Mon - Fri: 9:00 AM - 6:00 PM<br>
                                    Sat: 10:00 AM - 4:00 PM<br>
                                    Sun: Closed
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Response Promise -->
                <div class="bg-primary-50 border border-primary-200 rounded-2xl p-8"
                     x-show="visible"
                     x-transition:enter="transition ease-out duration-800 delay-200"
                     x-transition:enter-start="opacity-0 transform translate-y-8"
                     x-transition:enter-end="opacity-100 transform translate-y-0">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-12 h-12 bg-primary-600 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-primary-900">Quick Response Guarantee</h3>
                    </div>
                    <p class="text-primary-800 leading-relaxed">
                        We understand that time is valuable. Our team commits to responding to all inquiries within 24 hours during business days, often much sooner.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
