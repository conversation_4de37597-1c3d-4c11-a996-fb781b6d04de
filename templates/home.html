{% extends "base.html" %}

{% block title %}Home - CorporatePro | Professional Business Solutions{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="relative overflow-hidden bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
        </svg>
    </div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Hero Content -->
            <div class="text-center lg:text-left" x-data="{ visible: false }" x-init="setTimeout(() => visible = true, 100)">
                <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6"
                    x-show="visible" 
                    x-transition:enter="transition ease-out duration-1000"
                    x-transition:enter-start="opacity-0 transform translate-y-8"
                    x-transition:enter-end="opacity-100 transform translate-y-0">
                    Elevate Your <span class="text-primary-200">Business</span> to New Heights
                </h1>
                
                <p class="text-xl lg:text-2xl text-primary-100 mb-8 leading-relaxed"
                   x-show="visible" 
                   x-transition:enter="transition ease-out duration-1000 delay-200"
                   x-transition:enter-start="opacity-0 transform translate-y-8"
                   x-transition:enter-end="opacity-100 transform translate-y-0">
                    Professional solutions designed to drive growth, efficiency, and success for modern enterprises.
                </p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
                     x-show="visible" 
                     x-transition:enter="transition ease-out duration-1000 delay-400"
                     x-transition:enter-start="opacity-0 transform translate-y-8"
                     x-transition:enter-end="opacity-100 transform translate-y-0">
                    <a href="/contact" 
                       class="bg-white text-primary-700 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary-50 transition-all duration-200 hover-lift corporate-shadow">
                        Get Started Today
                    </a>
                    <a href="/about" 
                       class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-700 transition-all duration-200">
                        Learn More
                    </a>
                </div>
            </div>
            
            <!-- Hero Image/Graphic -->
            <div class="relative" x-data="{ visible: false }" x-init="setTimeout(() => visible = true, 300)">
                <div class="relative z-10"
                     x-show="visible" 
                     x-transition:enter="transition ease-out duration-1000"
                     x-transition:enter-start="opacity-0 transform scale-95"
                     x-transition:enter-end="opacity-100 transform scale-100">
                    <!-- Placeholder for hero graphic - using CSS shapes for now -->
                    <div class="w-full max-w-lg mx-auto">
                        <div class="relative">
                            <!-- Main shape -->
                            <div class="w-80 h-80 bg-gradient-to-br from-primary-400 to-primary-600 rounded-3xl transform rotate-6 shadow-2xl"></div>
                            <!-- Accent shapes -->
                            <div class="absolute -top-6 -right-6 w-32 h-32 bg-primary-300 rounded-2xl transform -rotate-12 opacity-80"></div>
                            <div class="absolute -bottom-4 -left-4 w-24 h-24 bg-primary-200 rounded-xl transform rotate-12 opacity-60"></div>
                            <!-- Content overlay -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center">
                                    <svg class="w-24 h-24 text-white mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    <p class="text-white font-semibold text-lg">Innovation</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Why Choose CorporatePro?
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                We deliver comprehensive solutions that transform businesses and drive sustainable growth through innovation and expertise.
            </p>
        </div>
        
        <!-- Features Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" x-data="{ inView: false }" x-init="setTimeout(() => inView = true, 300)">
            <!-- Feature 1 -->
            <div class="text-center group hover-lift"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600"
                 x-transition:enter-start="opacity-0 transform translate-y-8"
                 x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Proven Results</h3>
                <p class="text-gray-600 leading-relaxed">
                    Track record of delivering measurable outcomes that exceed expectations and drive business growth.
                </p>
            </div>
            
            <!-- Feature 2 -->
            <div class="text-center group hover-lift"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600 delay-100"
                 x-transition:enter-start="opacity-0 transform translate-y-8"
                 x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Innovation First</h3>
                <p class="text-gray-600 leading-relaxed">
                    Cutting-edge solutions powered by the latest technologies and industry best practices.
                </p>
            </div>
            
            <!-- Feature 3 -->
            <div class="text-center group hover-lift"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600 delay-200"
                 x-transition:enter-start="opacity-0 transform translate-y-8"
                 x-transition:enter-end="opacity-100 transform translate-y-0">
                <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Expert Team</h3>
                <p class="text-gray-600 leading-relaxed">
                    Dedicated professionals with deep industry knowledge and commitment to your success.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-8" x-data="{ inView: false }" x-init="setTimeout(() => inView = true, 500)">
            <!-- Stat 1 -->
            <div class="text-center"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100">
                <div class="text-4xl lg:text-5xl font-bold text-primary-600 mb-2" x-data="{ count: 0 }" x-init="inView && setInterval(() => { if(count < 500) count += 10 }, 20)">
                    <span x-text="count + '+'"></span>
                </div>
                <p class="text-gray-600 font-medium">Happy Clients</p>
            </div>
            
            <!-- Stat 2 -->
            <div class="text-center"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600 delay-100"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100">
                <div class="text-4xl lg:text-5xl font-bold text-primary-600 mb-2" x-data="{ count: 0 }" x-init="inView && setInterval(() => { if(count < 98) count += 2 }, 50)">
                    <span x-text="count + '%'"></span>
                </div>
                <p class="text-gray-600 font-medium">Success Rate</p>
            </div>
            
            <!-- Stat 3 -->
            <div class="text-center"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600 delay-200"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100">
                <div class="text-4xl lg:text-5xl font-bold text-primary-600 mb-2" x-data="{ count: 0 }" x-init="inView && setInterval(() => { if(count < 15) count += 1 }, 100)">
                    <span x-text="count + '+'"></span>
                </div>
                <p class="text-gray-600 font-medium">Years Experience</p>
            </div>
            
            <!-- Stat 4 -->
            <div class="text-center"
                 x-show="inView" 
                 x-transition:enter="transition ease-out duration-600 delay-300"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100">
                <div class="text-4xl lg:text-5xl font-bold text-primary-600 mb-2" x-data="{ count: 0 }" x-init="inView && setInterval(() => { if(count < 24) count += 1 }, 80)">
                    <span x-text="count + '/7'"></span>
                </div>
                <p class="text-gray-600 font-medium">Support Available</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-primary-600 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl lg:text-4xl font-bold mb-6">
            Ready to Transform Your Business?
        </h2>
        <p class="text-xl text-primary-100 mb-8 leading-relaxed">
            Join hundreds of successful companies that trust CorporatePro for their business solutions.
        </p>
        <a href="/contact" 
           class="inline-block bg-white text-primary-700 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-primary-50 transition-all duration-200 hover-lift corporate-shadow">
            Start Your Journey Today
        </a>
    </div>
</section>
{% endblock %}
