#!/usr/bin/env python3
"""
Simple test script to verify the website functionality
"""

import requests
import sys

BASE_URL = "http://127.0.0.1:8888"

def test_endpoints():
    """Test all main endpoints"""
    endpoints = [
        "/",
        "/about", 
        "/contact",
        "/health"
    ]
    
    print("Testing website endpoints...")
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            status = "✅ PASS" if response.status_code == 200 else "❌ FAIL"
            print(f"{status} {endpoint} - Status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ FAIL {endpoint} - Error: {e}")
    
    print()

def test_contact_form():
    """Test contact form submission"""
    print("Testing contact form submission...")
    
    form_data = {
        "first_name": "Test",
        "last_name": "User", 
        "email": "<EMAIL>",
        "phone": "+**********",
        "company": "Test Company",
        "subject": "general",
        "message": "This is a test message from the automated test script.",
        "privacy_consent": "on"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/contact", data=form_data)
        if response.status_code == 200 and "Message sent successfully" in response.text:
            print("✅ PASS Contact form submission - Form submitted successfully")
        else:
            print(f"❌ FAIL Contact form submission - Status: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
    except requests.exceptions.RequestException as e:
        print(f"❌ FAIL Contact form submission - Error: {e}")
    
    print()

def test_static_resources():
    """Test that static resources are accessible"""
    print("Testing static resource accessibility...")
    
    # Test if TailwindCSS, HTMX, and Alpine.js are loading
    try:
        response = requests.get(f"{BASE_URL}/")
        content = response.text
        
        checks = [
            ("TailwindCSS", "tailwindcss.com" in content),
            ("HTMX", "htmx.org" in content),
            ("Alpine.js", "alpinejs" in content)
        ]
        
        for name, check in checks:
            status = "✅ PASS" if check else "❌ FAIL"
            print(f"{status} {name} - {'Found' if check else 'Not found'} in HTML")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ FAIL Static resources test - Error: {e}")
    
    print()

def main():
    print("🚀 CorporatePro Website Test Suite")
    print("=" * 40)
    
    test_endpoints()
    test_contact_form()
    test_static_resources()
    
    print("Test suite completed!")

if __name__ == "__main__":
    main()
