<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Menu Test</title>
    <script src="https://cdn.tailwindcss.com?v=3.4.17"></script>
    <script defer src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js"></script>
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-lg">
        <h1 class="text-2xl font-bold mb-6">Mobile Menu Test</h1>
        
        <!-- Test Alpine.js -->
        <div x-data="{ open: false }" class="mb-6">
            <button @click="open = !open" 
                    class="bg-blue-500 text-white px-4 py-2 rounded">
                Toggle Test Menu
            </button>
            <div x-show="open" 
                 x-transition
                 class="mt-4 p-4 bg-gray-100 rounded">
                Alpine.js is working! ✅
            </div>
        </div>
        
        <!-- Test Mobile Menu Component -->
        <div x-data="{ mobileMenuOpen: false }" class="border rounded-lg p-4">
            <div class="flex justify-between items-center">
                <span class="font-semibold">Mobile Menu Test</span>
                <button @click="mobileMenuOpen = !mobileMenuOpen"
                        class="p-2 text-gray-600 hover:text-gray-900">
                    <svg class="w-6 h-6" :class="{ 'hidden': mobileMenuOpen, 'block': !mobileMenuOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                    <svg class="w-6 h-6" :class="{ 'block': mobileMenuOpen, 'hidden': !mobileMenuOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div x-show="mobileMenuOpen" 
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform -translate-y-4"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform translate-y-0"
                 x-transition:leave-end="opacity-0 transform -translate-y-4"
                 class="mt-4 space-y-2"
                 x-cloak>
                <a href="#" @click="mobileMenuOpen = false" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">Home</a>
                <a href="#" @click="mobileMenuOpen = false" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">About</a>
                <a href="#" @click="mobileMenuOpen = false" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">Contact</a>
            </div>
        </div>
        
        <div class="mt-6 text-sm text-gray-600">
            <p>✅ If you can see this page and the buttons work, Alpine.js is functioning correctly.</p>
            <p>✅ The hamburger menu should toggle between ☰ and ✕ icons.</p>
            <p>✅ The menu should slide down/up with smooth transitions.</p>
        </div>
    </div>
    
    <script>
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js initialized successfully!');
        });
    </script>
</body>
</html>
