from fastapi import <PERSON><PERSON><PERSON>, Request, Form
from fastapi.templating import <PERSON><PERSON>2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from datetime import datetime
from typing import Optional

# Initialize FastAPI app
app = FastAPI(
    title="CorporatePro",
    description="Professional Business Solutions Website",
    version="1.0.0"
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Initialize templates
templates = Jinja2Templates(directory="templates")

# Contact form submission handler
@app.post("/contact")
async def submit_contact_form(
    request: Request,
    first_name: str = Form(...),
    last_name: str = Form(...),
    email: str = Form(...),
    phone: Optional[str] = Form(None),
    company: Optional[str] = Form(None),
    subject: str = Form(...),
    message: str = Form(...),
    privacy_consent: Optional[str] = Form(None)
):
    """Handle contact form submission and save to text file"""

    # Validate required fields
    if not all([first_name, last_name, email, subject, message]):
        return HTMLResponse(
            content="""
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-red-800 font-medium">Please fill in all required fields.</p>
                </div>
            </div>
            """,
            status_code=400
        )

    # Check privacy consent
    if not privacy_consent:
        return HTMLResponse(
            content="""
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-red-800 font-medium">Please accept the privacy policy to continue.</p>
                </div>
            </div>
            """,
            status_code=400
        )

    # Create contact entry
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    contact_entry = f"""
=== CONTACT FORM SUBMISSION ===
Timestamp: {timestamp}
Name: {first_name} {last_name}
Email: {email}
Phone: {phone or 'Not provided'}
Company: {company or 'Not provided'}
Subject: {subject}
Message: {message}
Privacy Consent: Yes
IP Address: {request.client.host if request.client else 'Unknown'}
User Agent: {request.headers.get('user-agent', 'Unknown')}
{'='*50}

"""

    # Save to text file in project root
    try:
        with open("contact_submissions.txt", "a", encoding="utf-8") as f:
            f.write(contact_entry)

        # Return success message
        return HTMLResponse(
            content="""
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <p class="text-green-800 font-medium">Message sent successfully!</p>
                        <p class="text-green-700 text-sm mt-1">Thank you for contacting us. We'll get back to you within 24 hours.</p>
                    </div>
                </div>
            </div>
            <script>
                // Reset form after successful submission
                setTimeout(() => {
                    document.querySelector('form').reset();
                }, 2000);
            </script>
            """,
            status_code=200
        )

    except Exception as e:
        # Return error message
        return HTMLResponse(
            content="""
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <p class="text-red-800 font-medium">Error sending message</p>
                        <p class="text-red-700 text-sm mt-1">Please try again later or contact us directly.</p>
                    </div>
                </div>
            </div>
            """,
            status_code=500
        )

# Route handlers
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Home page"""
    return templates.TemplateResponse("home.html", {"request": request})

@app.get("/about", response_class=HTMLResponse)
async def about(request: Request):
    """About page"""
    return templates.TemplateResponse("about.html", {"request": request})

@app.get("/contact", response_class=HTMLResponse)
async def contact(request: Request):
    """Contact page"""
    return templates.TemplateResponse("contact.html", {"request": request})

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}



if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
